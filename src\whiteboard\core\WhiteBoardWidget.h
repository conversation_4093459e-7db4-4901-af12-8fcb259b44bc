#ifndef WhiteBoardWidget_H
#define WhiteBoardWidget_H

#include <QGraphicsView>
#include <QTouchEvent>
#include <QMouseEvent>
#include <QPixmap>
#include <QElapsedTimer>
#include <QMutex>
#include <QPointer>
#include <QHash>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QDateTime>
#include <QOpenGLWidget>
#include <QOpenGLContext>
#include <QSurfaceFormat>
#include <QSvgRenderer>
#include <QThread>
#include <QFuture>
#include <QtConcurrent>
#include "WhiteBoardTypes.h"
#include "../optimization/OptimizedDrawingState.h"
#include "../utils/FeatheringRenderer.h"

#include "../ui/SelectionUITypes.h"
#include "../utils/DashPathConverter.h"
#include "../commands/GraphicsItemState.h"


class WhiteBoardScene;
class DrawItem;
class SelectionUIManager;
class CommandManager;

// 图形项快照数据结构
struct DrawItemSnapshot {
    DrawItem* originalItem;           // 原始图形项指针
    QPainterPath path;                // 路径数据
    QPen pen;                         // 画笔属性
    QBrush brush;                     // 画刷属性
    ToolType toolType;                // 工具类型
    QPointF position;                 // 位置
    QTransform transform;             // 变换
    qreal zValue;                     // Z值

    DrawItemSnapshot() : originalItem(nullptr), toolType(ToolType::FreeDraw), zValue(0) {}
};

// 切割结果数据结构
struct ErasureResult {
    DrawItemSnapshot snapshot;        // 原始图形项快照
    QList<QPainterPath> resultPaths;  // 切割后的路径列表
    bool shouldRemove;                // 是否应该删除原图形项

    ErasureResult() : shouldRemove(false) {}
};

class EraseCommand;

/**
 * @brief 简化的白板视图类 - 直接管理所有绘制逻辑
 *
 * 核心功能：
 * 1. drawForeground作为活动层：绘制正在进行的内容
 * 2. drawItems作为历史层：绘制已完成的图形
 * 3. 统一的工具管理：直接在View中处理所有工具逻辑
 * 4. 简化的事件处理：无需复杂的工具切换机制
 */
class WhiteBoardWidget : public QGraphicsView
{
    Q_OBJECT

public:
    // 使用公共的工具类型枚举
    using ToolType = ::ToolType;

    // 绘制状态结构
    struct DrawingState {
        QPointF startPoint;
        QPointF currentPoint;
        QPainterPath activePath;
        QPen pen;
        QBrush brush;
        bool isDrawing = false;
        ToolType toolType = ToolType::FreeDraw;
        qint64 startTime = 0;
    };

public:
    explicit WhiteBoardWidget(QWidget* parent = nullptr);
    ~WhiteBoardWidget();

    // 场景设置
    void setWhiteBoardScene(WhiteBoardScene* scene);

    // 工具设置接口
    void setCurrentTool(ToolType tool);
    ToolType getCurrentTool() const { return m_currentTool; }

    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    void setEraserSize(const QSizeF& size);

    // 性能检测控制
    void enableDrawingPerformanceProfiler(bool enabled);
    void resetDrawingPerformanceStats();
    void printDrawingPerformanceSummary();

    // 绘制接口
    void startDrawing(int touchId, const QPointF& point);
    void continueDrawing(int touchId, const QPointF& point);
    void finishDrawing(int touchId);
    void cancelDrawing(int touchId);
    void cancelAllDrawing();

    // 选择功能
    void startLassoSelection(const QPointF& point);
    void continueLassoSelection(const QPointF& point);
    void finishLassoSelection();
    void clearSelection();



    // 性能优化配置
    void enableIncrementalUpdate(bool enable);
    void setDrawingBatchSize(int size);
    bool isIncrementalUpdateEnabled() const;
    void enableLayeredCaching(bool enable);
    bool isLayeredCachingEnabled() const;

    // 分层缓存管理
    void invalidateHistoryCache();       // 使历史层缓存失效
    void clearHistoryCache();            // 清空历史层缓存

protected:
    // 核心绘制方法
    void drawForeground(QPainter* painter, const QRectF& rect) override;
    void drawItems(QPainter* painter, int numItems,
                   QGraphicsItem* items[], const QStyleOptionGraphicsItem options[]) override;
    void drawBackground(QPainter* painter, const QRectF& rect) override;

    // 事件处理
    bool event(QEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void scrollContentsBy(int dx, int dy) override;

public:
    // 命令系统接口
    bool canUndo() const;
    bool canRedo() const;
    bool undo();
    bool redo();
    void clearHistory();

    // 坐标转换
    QPointF mapToSceneWithDPI(const QPointF& viewPoint);

    // 事件处理
    bool handleTouchEvent(QTouchEvent* event);



    // 多指绘制控制
    void setMultiTouchEnabled(bool enabled);
    bool isMultiTouchEnabled() const;
    int getMaxTouchPoints() const;


    void setupEnhancedRenderingHints(QPainter* painter) const;

    QPen getAntialiasedPen(const QPen& originalPen) const;


    // 性能测试功能
    void performRenderingBenchmark(int iterations = 100);

    // 性能优化配置
    void configureDirtyRegionOptimization();  // 配置脏区域管理器优化

    // 虚线处理配置
    void setDashToSolidConversionEnabled(bool enabled) { m_dashToSolidConversionEnabled = enabled; }

    // OpenGL视口配置
    void setupOpenGLViewport();

private:

    // 图形项创建
    QGraphicsItem* createDrawItem(const DrawingState& state);
    QGraphicsItem* createDrawItemFromOptimizedState(const OptimizedDrawingState& state);

    // UI绘制
    void drawActiveDrawings(QPainter* painter, const QRectF& clipRect);  // 支持分层绘制

    void drawUIElements(QPainter* painter, const QRectF& rect);
    void drawLassoPath(QPainter* painter);
    void drawEraser(QPainter* painter);
    void drawGrid(QPainter* painter, const QRectF& rect);
    void drawBackgroundColor(QPainter* painter, const QRectF& rect);

    // 分层缓存绘制方法
    void updateHistoryLayerCache();      // 更新历史层缓存
    void drawHistoryLayer(QPainter* painter);  // 绘制历史层

    // 选择处理
    void updateSelection();
    bool isPointInLasso(const QPointF& point);

    // 精确套索选择
    QList<QGraphicsItem*> performLassoSelection(const QPainterPath& lassoPath, const QList<QPointF>& lassoPoints);
    QGraphicsItem* selectTopItemAtLassoPoints(const QList<QPointF>& lassoPoints);
    QList<QGraphicsItem*> selectItemsByLinearLasso(const QList<QPointF>& lassoPoints);
    bool isLassoPathLinear(const QList<QPointF>& lassoPoints);
    QPainterPath createClosedPath(const QPainterPath& openPath);
    bool isGraphicSelectedByLasso(QGraphicsItem* item, const QPainterPath& lassoPath, const QList<QPointF>& lassoPoints);

    // 橡皮擦处理
    void startErasing(const QPointF& point);
    void continueErasing(const QPointF& point);
    void finishErasing();
    void eraseAtPosition(const QPointF& point);
    void loadEraserImage();

    // 橡皮擦精准切割
    void performPrecisionErasure(const QList<QGraphicsItem*>& items, const QRectF& eraserRect);
    void eraseFromFreeDrawItem(DrawItem* item, const QRectF& eraserRect);
    QGraphicsItem* createDrawItemFromPath(DrawItem* originalItem, const QPainterPath& path, const QPen& pen, const QBrush& brush);

    // 异步切割相关
    void startAsyncErasureProcessing();  // 开始异步切割处理
    void applyAsyncErasureResults();     // 应用异步切割结果
    DrawItemSnapshot createItemSnapshot(DrawItem* item);  // 创建图形项快照
    static QList<ErasureResult> processErasureInBackground(const QList<DrawItemSnapshot>& snapshots, const QList<QRectF>& eraserRects);  // 后台切割函数

    // 橡皮擦辅助方法
    static QList<QPainterPath> splitPathIntoSubpaths(const QPainterPath& path);

    // 获取当前擦除区域（供DrawItem使用）
    QList<QRectF> getCurrentEraserRects() const;

    // 多指绘制控制
    bool isMultiTouchAllowed(int touchId) const;
    bool hasTouchDrawing() const;  // 检查是否有活动的触屏绘制

    // 橡皮擦属性设置
    QSizeF getEraserSize() const;
    void setEraserWidth(qreal width);
    qreal getEraserWidth() const;



signals:
    void drawingStarted(int touchId);
    void drawingFinished(int touchId);
    void selectionChanged();
    void toolChanged(ToolType tool);


private:
    // 核心数据
    QHash<int, OptimizedDrawingState> m_activeDrawings;  // touchId -> 优化的绘制状态
    ToolType m_currentTool = ToolType::FreeDraw;
    QPen m_currentPen;
    QBrush m_currentBrush;

    // 性能优化配置
    bool m_enableIncrementalUpdate = true;
    bool m_enableDirtyRegionOptimization = true;
    bool m_enableLayeredCaching = true;  // 分层缓存开关

    // 批量更新优化
    QElapsedTimer m_batchUpdateTimer;    // 批量更新计时器
    QRectF m_accumulatedDirtyRegion;     // 累积的脏区域
    int m_updateInterval = 16;    // 更新间隔(ms)

    // 场景引用
    QPointer<WhiteBoardScene> m_scene;

    // 分层缓存系统
    QPixmap m_historyLayerCache;         // 历史图形缓存
    bool m_historyLayerDirty = true;     // 历史层是否需要重绘
    QRectF m_lastHistoryBounds;          // 上次历史层边界

    // UI管理器
    QPointer<SelectionUIManager> m_selectionUIManager;
    
    // 特殊工具处理
    static const int SPECIAL_TOOL_TOUCH_ID = -2;
    int m_specialToolTouchId = SPECIAL_TOOL_TOUCH_ID; // 避免与鼠标的-1冲突

    // 套索选择状态
    QVector<QPointF> m_lassoPath;           // 原始点列表（用于选择逻辑）
    IncrementalPathBuilder m_lassoPathBuilder; // 优化的路径构建器（用于绘制）
    QList<QGraphicsItem*> m_selectedItems;

    // 橡皮擦状态
    QPointF m_eraserPosition;
    QSizeF m_eraserSize = QSizeF(73, 82); // 默认橡皮擦大小
    QPixmap m_eraserPixmap;               // WebP后备图片
    QSvgRenderer* m_eraserSvgRenderer = nullptr;  // SVG渲染器（优先使用）
    bool m_isErasing = false;             // 橡皮擦活动状态

    // 橡皮擦状态管理
    QList<GraphicsItemState> m_eraseBeforeStates;  // 擦除前的场景状态
    QList<QRectF> m_eraserRects;          // 擦除过程中的所有橡皮擦区域

    // 异步切割相关
    QList<DrawItemSnapshot> m_itemSnapshots;       // 擦除开始时的图形项快照
    QFuture<QList<ErasureResult>> m_asyncErasureTask;  // 异步切割任务
    QMutex m_erasureResultsMutex;                   // 结果访问互斥锁

    qreal m_devicePixelRatio = 1.0;


    // 配置参数
    int m_gridSize = 20;
    QColor m_backgroundColor = Qt::transparent;  // 设置背景为透明
    QColor m_gridColor = QColor(200, 200, 200, 100);

    // 多指绘制配置
    static const int MAX_TOUCH_POINTS = 10;     // 最大触控点数量
    bool m_multiTouchEnabled = true;            // 多指绘制开关


    // 自适应抗锯齿配置
    mutable int m_performanceCounter = 0;       // 性能计数器
    mutable qreal m_averageRenderTime = 16.0;   // 平均渲染时间（毫秒）


    // 虚线处理工具
    DashPathConverter m_dashConverter;
    bool m_dashToSolidConversionEnabled = true; // 默认开启，解决虚线问题


};

#endif // WhiteBoardWidget_H
