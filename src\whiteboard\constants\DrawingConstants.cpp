#include "DrawingConstants.h"

namespace DrawingConstants {

    // 全局擦除路径
    QPainterPath g_currentEraserPath;

    void addEraserRect(const QRectF& rect)
    {
        QPainterPath rectPath;
        rectPath.addRect(rect);

        if (g_currentEraserPath.isEmpty()) {
            g_currentEraserPath = rectPath;
        } else {
            // 使用united()方法合并路径，实现平滑连接
            g_currentEraserPath = g_currentEraserPath.united(rectPath);
        }
    }

    QPainterPath getCurrentEraserPath()
    {
        return g_currentEraserPath;
    }

    void clearEraserPath()
    {
        g_currentEraserPath = QPainterPath();
    }

} // namespace DrawingConstants
