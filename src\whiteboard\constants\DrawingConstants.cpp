#include "DrawingConstants.h"

namespace DrawingConstants {

    // 全局擦除路径
    QPainterPath g_currentEraserPath;

    // 未完成的切割路径数组
    QList<QPainterPath> g_clipPaths;

    void addEraserRect(const QRectF& rect)
    {
        QPainterPath rectPath;
        rectPath.addRect(rect);

        if (g_currentEraserPath.isEmpty()) {
            g_currentEraserPath = rectPath;
        } else {
            // 使用united()方法合并路径，实现平滑连接
            g_currentEraserPath = g_currentEraserPath.united(rectPath);
        }
    }

    void setCurrentEraserPath(const QPainterPath& path)
    {
        // 如果当前路径不为空，先添加到切割数组
        if (!g_currentEraserPath.isEmpty()) {
            g_clipPaths.append(g_currentEraserPath);
        }

        // 设置新的当前路径
        g_currentEraserPath = path;

        // 如果新路径不为空，也添加到切割数组
        if (!path.isEmpty()) {
            g_clipPaths.append(path);
        }
    }

    QPainterPath getCurrentEraserPath()
    {
        return g_currentEraserPath;
    }

    QList<QPainterPath> getClipPaths()
    {
        return g_clipPaths;
    }

    QPainterPath takeFirstClipPath()
    {
        if (g_clipPaths.isEmpty()) {
            return QPainterPath(); // 返回空路径
        }

        QPainterPath firstPath = g_clipPaths.first();
        g_clipPaths.removeFirst(); // 删除首条数据
        return firstPath;
    }

    bool hasClipPaths()
    {
        return !g_clipPaths.isEmpty();
    }

    void clearAllEraserData()
    {
        g_currentEraserPath = QPainterPath();
        g_clipPaths.clear();
    }

} // namespace DrawingConstants
