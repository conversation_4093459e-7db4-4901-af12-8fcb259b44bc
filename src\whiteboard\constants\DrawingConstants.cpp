#include "DrawingConstants.h"

namespace DrawingConstants {

    // 全局擦除区域列表
    QList<QRectF> g_currentEraserRects;

    void setCurrentEraserRects(const QList<QRectF>& rects)
    {
        g_currentEraserRects = rects;
    }

    QList<QRectF> getCurrentEraserRects()
    {
        return g_currentEraserRects;
    }

    void clearEraserRects()
    {
        g_currentEraserRects.clear();
    }

} // namespace DrawingConstants
