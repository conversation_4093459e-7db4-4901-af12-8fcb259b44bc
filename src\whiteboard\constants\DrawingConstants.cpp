#include "DrawingConstants.h"

namespace DrawingConstants {

    // 全局擦除路径
    QPainterPath g_currentEraserPath;

    // 未完成的切割路径数组
    QList<QPainterPath> g_clipPaths;

    void addEraserRect(const QRectF& rect)
    {
        QPainterPath rectPath;
        rectPath.addRect(rect);

        if (g_currentEraserPath.isEmpty()) {
            g_currentEraserPath = rectPath;
        } else {
            // 使用united()方法合并路径，实现平滑连接
            g_currentEraserPath = g_currentEraserPath.united(rectPath);
        }
    }

    QPainterPath getCurrentEraserPath()
    {
        return g_currentEraserPath;
    }

    QList<QPainterPath> getClipPaths()
    {
        return g_clipPaths;
    }

    void addCurrentPathToClipQueue()
    {
        if (!g_currentEraserPath.isEmpty()) {
            g_clipPaths.append(g_currentEraserPath);
            g_currentEraserPath = QPainterPath(); // 清空当前路径
        }
    }

    void clearAllEraserData()
    {
        g_currentEraserPath = QPainterPath();
        g_clipPaths.clear();
    }

} // namespace DrawingConstants
