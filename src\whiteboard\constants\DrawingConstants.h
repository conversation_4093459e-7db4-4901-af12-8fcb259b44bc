#ifndef DRAWINGCONSTANTS_H
#define DRAWINGCONSTANTS_H

#include <QVector>
#include <QRectF>
#include <QList>

/**
 * @brief 绘制相关常量定义
 */
namespace DrawingConstants {

    // ========== 虚线模式常量 ==========

    /**
     * @brief 自定义虚线模式 - 4:6比例
     * 4个单位实线，6个单位空隙
     */
    const QVector<qreal> CUSTOM_DASH_PATTERN = {4.0, 6.0};

    // ========== 全局擦除区域管理 ==========

    /**
     * @brief 全局擦除区域列表
     * 所有DrawItem都可以从这里获取当前的擦除区域
     */
    extern QList<QRectF> g_currentEraserRects;

    /**
     * @brief 设置当前擦除区域
     */
    void setCurrentEraserRects(const QList<QRectF>& rects);

    /**
     * @brief 获取当前擦除区域
     */
    QList<QRectF> getCurrentEraserRects();

    /**
     * @brief 清空擦除区域
     */
    void clearEraserRects();

} // namespace DrawingConstants

#endif // DRAWINGCONSTANTS_H
