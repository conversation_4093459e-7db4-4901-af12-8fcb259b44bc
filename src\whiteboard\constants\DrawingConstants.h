#ifndef DRAWINGCONSTANTS_H
#define DRAWINGCONSTANTS_H

#include <QVector>
#include <QPainterPath>

/**
 * @brief 绘制相关常量定义
 */
namespace DrawingConstants {

    // ========== 虚线模式常量 ==========

    /**
     * @brief 自定义虚线模式 - 4:6比例
     * 4个单位实线，6个单位空隙
     */
    const QVector<qreal> CUSTOM_DASH_PATTERN = {4.0, 6.0};

    // ========== 全局擦除区域管理 ==========

    /**
     * @brief 全局擦除路径
     * 所有DrawItem都可以从这里获取当前的擦除区域
     */
    extern QPainterPath g_currentEraserPath;

    /**
     * @brief 添加擦除区域到全局路径
     */
    void addEraserRect(const QRectF& rect);

    /**
     * @brief 获取当前擦除路径
     */
    QPainterPath getCurrentEraserPath();

    /**
     * @brief 清空擦除路径
     */
    void clearEraserPath();

} // namespace DrawingConstants

#endif // DRAWINGCONSTANTS_H
